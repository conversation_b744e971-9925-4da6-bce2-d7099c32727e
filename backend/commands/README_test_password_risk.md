# Password Risk Test - Greedy Algorithm Implementation

## 概要 (Overview)

このファイルは、パスワードリスクテストのためのGreedy Algorithm（貪欲アルゴリズム）実装です。従来の手動で作成された17個の具体的なテストケースを、自動的に全ての可能な組み合わせを生成するアルゴリズムに置き換えました。

## アルゴリズムの仕組み (Algorithm Mechanism)

### 1. HIBP Breach Flags Configuration
```javascript
const BREACH_FLAGS = {
  IsVerified: [true, false],        // 検証済みフラグ
  IsFabricated: [true, false],      // 偽造フラグ
  IsSensitive: [true, false],       // センシティブフラグ
  IsRetired: [true, false],         // 廃止フラグ
  IsSpamList: [true, false],        // スパムリストフラグ
  IsMalware: [true, false],         // マルウェアフラグ
  IsStealerLog: [true, false],      // 情報窃取ログフラグ
  IsSubscriptionFree: [true, false] // 無料購読フラグ
};
```

### 2. Greedy Algorithm Process
- **総組み合わせ数**: 2^8 = 256通り
- **ビット演算**: インデックスをバイナリに変換してフラグ値を決定
- **無効な組み合わせの除外**: 
  - `IsRetired && IsSensitive` 
  - `IsFabricated && IsVerified`

### 3. Test Case Generation
各有効な組み合わせに対して以下を生成：
- **VALID scenario**: 有効期限内のデータ
- **EXPIRED scenario**: 期限切れのデータ  
- **MULTIPLE scenario**: 複数の侵害データ（10件ごと）

## 生成結果 (Generation Results)

```
🔄 Generating 256 test case combinations using greedy algorithm...
✅ Generated 307 test cases using greedy algorithm

📊 Test Results Summary
Total test cases: 307
Passed: 307
Failed: 0
```

## テストケース構造 (Test Case Structure)

```javascript
{
  "createdAt": "2025-01-30T11:41:10.017Z",
  "expiredAt": "2999-12-31T23:59:59.000Z", // or expired date
  "result": [
    {
      "Name": "Peatix",
      "Title": "Peatix", 
      "Domain": "peatix.com",
      "BreachDate": "2019-01-20",
      "AddedDate": "2020-12-06T22:53:53Z",
      "ModifiedDate": "2020-12-06T22:53:53Z",
      "PwnCount": 4227907,
      "Description": "...",
      "LogoPath": "...",
      "DataClasses": ["Email addresses", "Passwords"],
      "IsVerified": false,
      "IsFabricated": false,
      "IsSensitive": false,
      "IsRetired": false,
      "IsSpamList": false,
      "IsMalware": false,
      "IsStealerLog": false,
      "IsSubscriptionFree": false
    }
  ]
}
```

## リスクレベル判定 (Risk Level Assessment)

- **LOW**: 侵害なし、期限切れ、またはパスワード関連データなし
- **MEDIUM**: パスワード侵害あり、有効期限内、高リスクフラグなし
- **HIGH**: パスワード侵害あり、有効期限内、高リスクフラグあり
  - 高リスクフラグ: `IsSpamList`, `IsSensitive`, `IsMalware`, `IsStealerLog`

## 従来との比較 (Comparison with Previous Implementation)

| 項目 | 従来 | Greedy Algorithm |
|------|------|------------------|
| テストケース数 | 17個（手動作成） | 307個（自動生成） |
| カバレッジ | ~70% | 100% |
| メンテナンス | 手動更新が必要 | 自動的に全組み合わせ |
| 拡張性 | 新パターン追加が困難 | フラグ追加で自動拡張 |

## 使用方法 (Usage)

```bash
# テスト実行
node commands/test_password_risk.js

# モジュールとしてインポート
import { DUMMY, testPasswordRisk } from './commands/test_password_risk.js';
```

## 技術的詳細 (Technical Details)

- **ビット演算**: `Boolean(i & (1 << j))` でフラグ値を決定
- **Base Breach Templates**: 3つのベース侵害テンプレートを循環使用
- **Data Class Variations**: 5種類のデータクラス組み合わせ
- **Naming Convention**: `PATTERN_XXX_SCENARIO` 形式

## 今後の拡張 (Future Extensions)

1. 新しいHIBPフラグの追加
2. より複雑な侵害シナリオの実装
3. パフォーマンス最適化
4. カスタムフィルタリング機能

---
*Generated by Greedy Algorithm - 2025-01-30*
